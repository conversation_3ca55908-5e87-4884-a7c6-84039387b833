using System;

namespace DoubleCamPreliminary
{
    /// <summary>
    /// 应用程序常量定义
    /// </summary>
    public static class Constants
    {
        #region 缓冲区大小
        public const int RESULT_BUFFER_SIZE = 128;
        public const int SN_INFO_BUFFER_SIZE = 1024;
        #endregion

        #region 错误消息
        public const string ERROR_MSG_INIT_FAILED = "程序初始化失败";
        public const string ERROR_MSG_SCAN_CONNECT_FAILED = "无法连接扫码程序";
        public const string ERROR_MSG_NETWORK_CHECK = "请检查网络 ret ";
        public const string ERROR_MSG_CONFIG_FILE = "请检查本地配置文件 sys.ini";
        public const string ERROR_MSG_BEAM_SN_LENGTH = "横梁SN码长度不符！";
        public const string ERROR_MSG_BRACKET_SN_LENGTH = "套筒SN码长度不符！";
        public const string ERROR_MSG_OLED_SN_LENGTH = "组件一SN码长度不符！";
        public const string ERROR_MSG_DB_WRITE_FAILED = "数据库写入失败";
        public const string ERROR_MSG_DB_QUERY_FAILED = "查询单目测试数据失败！";
        public const string ERROR_MSG_SN_CHECK = "请检查横梁SN或套筒SN是否正确！";
        public const string MSG_RESCAN_OR_CONTACT = "请重新扫码或联系管理员";
        #endregion

        #region 配置值
        public const string MODE_ONLINE = "online";
        public const string MODE_OFFLINE = "offline";
        public const string CONFIG_MODE_NET = "net";
        public const string CONFIG_MODE_LOCAL = "local";
        public const string BOOL_TRUE = "true";
        public const string BOOL_FALSE = "false";
        #endregion

        #region UI相关
        public const string WINDOW_TITLE_PREFIX = "双目预检 ";
        public const string LINE_PREFIX = " 线号：";
        public const string PROJECT_PREFIX = " 项目:";
        #endregion

        #region 数据格式
        public const string DECIMAL_FORMAT_4 = "F4";
        public const string DATE_FORMAT_VERSION = "yyyyMMdd";
        #endregion
    }
}
